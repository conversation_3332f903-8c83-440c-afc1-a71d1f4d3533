
import { Mail, Phone, MapPin, Clock, Star } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="py-16 px-4" role="contentinfo" aria-label="Website Footer">
      <div className="max-w-6xl mx-auto">
        {/* Main Footer Content */}
        <div className="suz-card-glass rounded-3xl border border-white/30 shadow-xl mb-8">
          <div className="p-8 md:p-12">
            {/* Footer Header */}
            <div className="text-center mb-12">
              <h2 className="suz-text-heading-xl gradient-text-animated mb-4">
                SUZ Reinigung
              </h2>
              <p className="suz-text-body-lg text-slate-600 max-w-2xl mx-auto">
                Ihr vertrauensvoller Partner für professionelle Reinigungsdienstleistungen
                in höchster Qualität und mit persönlichem Service.
              </p>
            </div>

            {/* Simplified Footer Information Grid */}
            <div className="grid md:grid-cols-2 gap-12 mb-12">
              {/* Service Information */}
              <div className="text-center">
                <h3 className="suz-text-heading-lg text-slate-700 mb-6 font-semibold">
                  Unser Service
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-center gap-3 text-slate-600">
                    <MapPin className="w-5 h-5 text-blue-500" />
                    <span className="suz-text-body-lg">Deutschland</span>
                  </div>
                  <div className="flex items-center justify-center gap-3 text-slate-600">
                    <Clock className="w-5 h-5 text-green-500" />
                    <span className="suz-text-body-lg">24/7 Verfügbar</span>
                  </div>
                  <div className="flex items-center justify-center gap-3 text-slate-600">
                    <Star className="w-5 h-5 text-yellow-500" />
                    <span className="suz-text-body-lg">Premium Qualität</span>
                  </div>
                </div>
              </div>

              {/* Quick Navigation */}
              <div className="text-center">
                <h3 className="suz-text-heading-lg text-slate-700 mb-6 font-semibold">
                  Navigation
                </h3>
                <div className="space-y-3">
                  <button
                    type="button"
                    className="suz-nav-link block w-full text-center suz-focus-ring"
                    onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    aria-label="Zur Startseite scrollen"
                  >
                    Startseite
                  </button>
                  <button
                    type="button"
                    className="suz-nav-link block w-full text-center suz-focus-ring"
                    onClick={() => document.getElementById('services')?.scrollIntoView({ behavior: 'smooth' })}
                    aria-label="Zu den Leistungen scrollen"
                  >
                    Unsere Leistungen
                  </button>
                  <button
                    type="button"
                    className="suz-nav-link block w-full text-center suz-focus-ring"
                    onClick={() => document.getElementById('team')?.scrollIntoView({ behavior: 'smooth' })}
                    aria-label="Zum Team scrollen"
                  >
                    Unser Team
                  </button>
                </div>
              </div>
            </div>

            {/* Website URL */}
            <div className="text-center mb-8">
              <a
                href="https://www.suzreinigung.de"
                target="_blank"
                rel="noopener noreferrer"
                className="suz-text-heading-lg gradient-text font-semibold hover:gradient-text-animated transition-all duration-300 suz-focus-ring"
                aria-label="Website besuchen: www.suzreinigung.de"
              >
                www.suzreinigung.de
              </a>
            </div>
          </div>
        </div>

        {/* Copyright Bar */}
        <div className="suz-card-glass rounded-2xl border border-white/20 px-6 py-4">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="suz-text-body-lg text-slate-600 text-center md:text-left">
              © 2024 SUZ Reinigung. Alle Rechte vorbehalten.
            </p>
            <div className="flex items-center gap-6 text-sm text-slate-500">
              <button
                type="button"
                className="hover:text-slate-700 transition-colors duration-200 suz-focus-ring"
                aria-label="Datenschutz-Informationen"
              >
                Datenschutz
              </button>
              <button
                type="button"
                className="hover:text-slate-700 transition-colors duration-200 suz-focus-ring"
                aria-label="Impressum anzeigen"
              >
                Impressum
              </button>
              <button
                type="button"
                className="hover:text-slate-700 transition-colors duration-200 suz-focus-ring"
                aria-label="Allgemeine Geschäftsbedingungen"
              >
                AGB
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
