import { useEffect, useRef } from 'react';

// TypeScript interface for company data
interface Company {
  id: string;
  name: string;
  type: 'hotel' | 'office' | 'medical' | 'residential' | 'retail' | 'restaurant';
}

// Sample company data - can be replaced with real client names later
const companies: Company[] = [
  { id: '1', name: 'Hotel Adlon Berlin', type: 'hotel' },
  { id: '2', name: 'Büro München GmbH', type: 'office' },
  { id: '3', name: '<PERSON><PERSON><PERSON>', type: 'medical' },
  { id: '4', name: 'Wohnanlage Parkblick', type: 'residential' },
  { id: '5', name: 'Restaurant Zur Linde', type: 'restaurant' },
  { id: '6', name: 'Einzelhandel Zentrum', type: 'retail' },
  { id: '7', name: 'Hotel Vier Jahreszeiten', type: 'hotel' },
  { id: '8', name: 'Büropark Frankfurt', type: 'office' },
  { id: '9', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', type: 'medical' },
  { id: '10', name: 'Residenz am See', type: 'residential' },
  { id: '11', name: 'Café Goldener Hirsch', type: 'restaurant' },
  { id: '12', name: 'Shopping Center Nord', type: 'retail' },
];

const CompanyShowcase = () => {
  const scrollRef = useRef<HTMLDivElement>(null);

  // Duplicate companies array for seamless infinite scroll
  const duplicatedCompanies = [...companies, ...companies];

  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (!scrollElement) return;

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (prefersReducedMotion) {
      // If user prefers reduced motion, disable animation
      scrollElement.style.animationPlayState = 'paused';
    }

    // Performance optimization: Use requestAnimationFrame for smooth rendering
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Start animation when component is visible
            scrollElement.style.animationPlayState = prefersReducedMotion ? 'paused' : 'running';
          } else {
            // Pause animation when component is not visible to save resources
            scrollElement.style.animationPlayState = 'paused';
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(scrollElement);

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <section
      className="suz-company-showcase overflow-hidden"
      aria-label="Unsere Kunden und Partner"
      role="region"
    >
      {/* Section Header */}
      <div className="text-center mb-12 px-4">
        <h2 className="suz-text-heading-lg font-semibold text-slate-700 dark:text-slate-200 mb-4">
          Vertrauen von <span className="gradient-text">führenden Unternehmen</span>
        </h2>
        <p className="suz-text-body-lg text-slate-500 dark:text-slate-400 max-w-2xl mx-auto">
          Über 500 zufriedene Kunden vertrauen auf unsere professionellen Reinigungsdienstleistungen
        </p>
      </div>

      {/* Animated Company Strip */}
      <div className="relative">
        {/* Gradient fade effects on edges */}
        <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-white dark:from-gray-900 via-white/80 dark:via-gray-900/80 to-transparent z-10 pointer-events-none"></div>
        <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-white dark:from-gray-900 via-white/80 dark:via-gray-900/80 to-transparent z-10 pointer-events-none"></div>
        
        {/* Scrolling container */}
        <div
          ref={scrollRef}
          className="suz-company-scroll flex gap-8 animate-scroll-right"
          role="list"
          aria-label="Kontinuierlich scrollende Liste unserer Kunden"
        >
          {duplicatedCompanies.map((company, index) => (
            <div
              key={`${company.id}-${index}`}
              className="suz-company-card flex-shrink-0"
              role="listitem"
              aria-label={`Kunde: ${company.name}, Kategorie: ${getCompanyTypeLabel(company.type)}`}
            >
              <div className="suz-card-glass rounded-2xl border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300 group">
                <div className="suz-company-card-content text-center">
                  <h3 className="suz-text-body-lg font-medium text-slate-700 dark:text-slate-200 group-hover:text-slate-800 dark:group-hover:text-slate-100 transition-colors duration-300">
                    {company.name}
                  </h3>
                  <div className="mt-2">
                    <span
                      className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-100 dark:border-blue-700/50"
                      aria-label={`Unternehmenstyp: ${getCompanyTypeLabel(company.type)}`}
                    >
                      {getCompanyTypeLabel(company.type)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Helper function to get localized company type labels
const getCompanyTypeLabel = (type: Company['type']): string => {
  const labels = {
    hotel: 'Hotel',
    office: 'Büro',
    medical: 'Praxis',
    residential: 'Wohnanlage',
    retail: 'Einzelhandel',
    restaurant: 'Gastronomie'
  };
  return labels[type];
};

export default CompanyShowcase;
