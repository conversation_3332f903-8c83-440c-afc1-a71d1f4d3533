
import { Building2, Home, Sparkles, Users, Briefcase, Shield } from 'lucide-react';

const Services = () => {
  const services = [
    {
      title: "Hotelzimmerreinigung",
      description: "Tiefenreinigung und tägliche Pflege für höchste Hygienestandards in Hotelzimmern.",
      icon: Building2
    },
    {
      title: "Teppichreinigung",
      description: "Tiefenreinigung für Teppiche und Polster. Wir entfernen Flecken, Gerüche und Allergene für ein frisches und hygienisches Raumklima.",
      icon: Home
    },
    {
      title: "Bodenreinigung",
      description: "Professionelle Pflege für Hartböden, Fliesen, Laminat und mehr. Wir sorgen für glänzende, hygienisch saubere Oberflächen.",
      icon: Sparkles
    },
    {
      title: "Gemeinschaftsräume",
      description: "Zuverlässige Reinigung von Treppenhäusern, Fluren und Gemeinschaftsbereichen für Mehrfamilienhäuser und Wohnanlagen.",
      icon: Users
    },
    {
      title: "Büroreinigung",
      description: "Professionelle Reinigung von Büroflächen und Arbeitsplätzen für ein sauberes und produktives Arbeitsumfeld.",
      icon: Briefcase
    },
    {
      title: "Desinfektion",
      description: "Gründliche Desinfektion von Räumen und Oberflächen zur Bekämpfung von Keimen, Bakterien und Viren für maximale Hygiene und Sicherheit.",
      icon: Shield
    }
  ];

  return (
    <section
      id="services"
      className="px-4"
      style={{
        padding: `var(--section-padding-xl) var(--space-4)`,
        marginTop: 'var(--space-16)' // Additional spacing from hero section
      }}
    >
      <div className="max-w-7xl mx-auto">
        <div className="text-center animate-fade-in" style={{ marginBottom: 'var(--space-20)' }}>
          <h2 className="suz-section-title text-slate-800 mb-8">
            Unsere <span className="gradient-text">Leistungen</span>
          </h2>
          <p className="suz-text-heading-lg text-slate-600 max-w-3xl mx-auto">
            Professionelle Reinigungslösungen für jeden Bedarf
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
          {services.map((service, index) => (
            <article
              key={index}
              className="suz-card-glass service-card-premium card-hover-enhanced rounded-3xl border border-white/30 group shadow-2xl animate-fade-in relative overflow-hidden"
              style={{
                animationDelay: `${index * 0.15}s`,
                padding: 'var(--component-padding-lg)'
              }}
            >
              {/* Shimmer effect overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-500 pointer-events-none"></div>

              <div className="icon-badge-enhanced mb-6 group-hover:scale-110 group-hover:rotate-3 relative z-10" role="img" aria-label={`Icon für ${service.title}`}>
                <service.icon
                  size={40}
                  className="text-blue-700 drop-shadow-lg transition-all duration-500 group-hover:text-blue-800 group-hover:scale-110"
                  strokeWidth={2.5}
                />
              </div>
              <h3 className="suz-text-heading-lg font-semibold text-slate-800 mb-6 text-reveal relative z-10">
                {service.title}
              </h3>
              <p className="suz-text-body-lg text-slate-600 text-reveal relative z-10" style={{ lineHeight: 'var(--line-height-relaxed)' }}>
                {service.description}
              </p>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
